using Atlas.Application.Models.MeetingMinute;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    /// <summary>
    /// Interface for meeting minute application services
    /// </summary>
    public interface IMeetingMinuteApplication
    {
        /// <summary>
        /// Creates a new meeting minute
        /// </summary>
        /// <param name="request">The meeting minute creation request</param>
        /// <returns>The created meeting minute response</returns>
        Task<Guid> CreateAsync(MeetingMinuteCreateRequest request);
        Task<MeetingMinuteGetResponse> GetMeetingMinuteAsync(Guid contentUuId);

        /// <summary>
        /// Generates HTML for meeting minute
        /// </summary>
        /// <param name="meetingContentUuid">Meeting content UUID</param>
        /// <param name="language">Language for generation</param>
        /// <param name="includeActions">Include actions in HTML</param>
        /// <returns>Generated HTML string</returns>
        Task<string> GenerateHtmlAsync(Guid meetingContentUuid, string language = "pt", bool includeActions = true);
    }
}
