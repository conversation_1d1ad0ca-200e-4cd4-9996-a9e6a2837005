namespace Atlas.Application.Models.MeetingMinute
{
    /// <summary>
    /// DTO para resposta da geração de HTML de meeting minute
    /// </summary>
    public class MeetingMinuteHtmlGenerateResponse
    {
        /// <summary>
        /// HTML gerado da ata da reunião
        /// </summary>
        public string html { get; set; }

        /// <summary>
        /// UUID do conteúdo da reunião
        /// </summary>
        public Guid meetingContentUuid { get; set; }

        /// <summary>
        /// Títu<PERSON> da reunião
        /// </summary>
        public string meetingTitle { get; set; }

        /// <summary>
        /// Data da reunião
        /// </summary>
        public DateTime meetingDate { get; set; }

        /// <summary>
        /// Nome do workgroup
        /// </summary>
        public string workgroupName { get; set; }

        /// <summary>
        /// Idioma utilizado na geração
        /// </summary>
        public string language { get; set; }

        /// <summary>
        /// Se as ações foram incluídas
        /// </summary>
        public bool actionsIncluded { get; set; }
    }
}
