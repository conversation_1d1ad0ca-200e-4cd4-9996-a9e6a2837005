namespace Atlas.Application.Models.MeetingMinute
{
    /// <summary>
    /// DTO para geração de HTML de meeting minute
    /// </summary>
    public class MeetingMinuteHtmlGenerateRequest
    {
        /// <summary>
        /// ID do cliente
        /// </summary>
        public int clientId { get; set; }

        /// <summary>
        /// ID do workgroup
        /// </summary>
        public int workgroupId { get; set; }

        /// <summary>
        /// UUID do conteúdo da reunião
        /// </summary>
        public Guid meetingContentUuid { get; set; }

        /// <summary>
        /// Idioma para geração do HTML (pt, en, es)
        /// </summary>
        public string language { get; set; } = "pt";

        /// <summary>
        /// Se deve incluir as tarefas/ações no HTML
        /// </summary>
        public bool includeActions { get; set; } = true;
    }
}
