using Atlas.CrossCutting.DTO.MeetingMinute;
using System;
using System.ComponentModel.DataAnnotations;

namespace Atlas.Application.Models.MeetingMinute
{
    /// <summary>
    /// DTO para resposta de operações com meeting minutes
    /// </summary>
    public class MeetingMinuteGetResponse
    {
        public int minuteId { get; set; }
        public int meetingId { get; set; }
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public string text { get; set; }
        public string minuteType { get; set; }
        public bool? published { get; set; }
        public DateTime? publishDate { get; set; }
        public int? publishUser { get; set; }
        public string publishUserName { get; set; }
        public DateTime createDate { get; set; }
        public int createUser { get; set; }
        public string createUserName { get; set; }
        public MeetingMinuteUacDto UAC { get; set; }
        public int totalNotes { get; set; }
    }
}